.mainContent {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  position: relative;
  width: 240px;
  height: 100%;
  background: #f8faff;
  transition: all 0.5s;
  z-index: 90;
  overflow: hidden;
}

.sidebar_open {
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 50;
  display: flex;
  flex-direction: row;
  padding: 12px 16px 12px 12px;


  .sidebar_newButton {
    border-radius: 16px;
    background: #FFF;
    box-sizing: border-box;
    border: 1px solid #005BF8;
    padding: 5px 11px;

    :global(.semi-button-content) {
      gap: 8px;
    }

    &:hover {
      border: 1px solid #005BF8;
      background: #FFF;
    }
  }

}

// 当屏幕小于1500时，支持横向滚动
@media screen and (max-width: 1500px) {
  .chat {
    overflow-x: scroll !important;
  }
}

.chat {
  flex: 1;
  height: 100%;
  background: #fff;
  position: relative;
  min-width: 500px;
}

.sidebar_top {
  margin: 16px;

  .title {
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
    color: #000;
    margin-left: 8px;
  }
}

.sidebar_newchatbtn {
  height: 38px;
  margin: 16px 10px 0 12px;
  padding: 8px;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  border-radius: 4px;
  background: rgba(0, 91, 248, 5%);
  box-sizing: border-box;
  border: none;
  color: #005bf8;
  cursor: pointer;
}

.sidebar_menu_item {
  margin: 8px 12px 0;
  height: 38px;
  // padding: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #000;

  &.linkActive {
    background: #fff;
    color: #000;
    font-weight: 500;
    box-sizing: border-box;
    box-shadow: 0 1px 6px 0 #D9DFEE;
  }

  &:hover {
    background: #fff;
    color: #000;
    box-shadow: 0 1px 4px 0 #e6ebf7;
  }

  .semi-icon {
    margin-right: 8px;
    margin-top: 2px;
  }
}

.sidebar_recent {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 8px 12px 12px;
  border-top: 1px solid #ebeef2;
  overflow: hidden;

  &_header {
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    padding: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
  }

  &_history {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .history_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    padding: 8px 10px 8px 32px;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    color: #666;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background: #EEF0FA;
      box-shadow: 0 1px 4px 0 #e6ebf7;
    }

    &_active {
      color: #000;
      background-color: #fff;
      box-shadow: 0 1px 4px 0 #e6ebf7;

      &:hover {
        background-color: #fff;
      }
    }

    &_text {
      flex: 1;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &_action {
      background-color: unset;
      transition: all 0.3s;

      &:hover {
        background-color: #ededed;
      }
    }
  }
}

.userEmail {
  text-align: justify;
  width: 115px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.meetingTitleOfMenu {
  padding: 0 !important;

  :global(.semi-typography) {
    max-width: 700px !important;
  }
}

.chattopbar {
  padding: 0 16px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 50;

  .splitLine {
    height: 14px !important;
    margin: 0 !important;
  }

  &_title {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    cursor: pointer;
    display: flex;
    align-items: center;

    .editIcon {
      margin-left: 4px;
      margin-top: 2px;
      display: none;
    }

    &:hover {
      .editIcon {
        display: block;
      }
    }

    &_text {
      line-height: 24px;
    }
  }

  &_right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  &_right_help {
    margin-right: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  &_userinfo {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
  }

  &_userinfo_avatar {
    margin-right: 8px;
  }

  &_userinfo_name {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    font-variation-settings: 'opsz'auto;
    color: #333;
  }

  &_userinfo_popover {
    padding: 8px;
    width: 200px;
  }

  &_userinfo_profile {
    display: flex;
    flex-direction: column;

    &_item {
      line-height: 38px;
      font-size: 14px;
      display: flex;
      align-items: center;

      span {
        margin-left: 4px;
        cursor: pointer;
      }
    }
  }

  &_logout {
    width: 100%;
  }
}

.chatMain {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: calc(100% - 56px);
  // overflow: hidden;
  position: relative;
  z-index: 40;
}

.chatMainWrapper {
  height: 100%;
}

.chatMain_doc {
  margin-top: -56px;
}

.history_action_popover {
  width: 124px;
  padding: 4px;
  border-radius: 4px;
}

.history_action_item {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  padding: 0 12px;

  &:hover {
    background: #f5f7fc;
  }
}

.history_action_icon {
  width: 20px;
  display: flex;
  align-items: center;
  margin-right: 4px;
}


.sdopen_newchat {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 32px;
  border-radius: 16px;
  background: #FFF;
  box-sizing: border-box;
  border: 1px solid #005BF8;
  padding: 10px 12px;
  margin-left: 20px;
  cursor: pointer;
  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: #005BF8;

  .sdopen_newchaticon {
    margin-right: 5px;
  }
}
