.meetingSummary {
  display: flex;
  flex-direction: row;
  height: 100%;
  max-width: 100vw;

  .meetingSummaryContainer {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    & + .meetingSummaryContainer {
      padding-right: 0;
    }

    &:only-child {
      padding: 0;
    }
  }

  :global {
    .semi-spin-wrapper {
      color: var(--semi-color-primary) !important;
    }
  }

  :global(.semi-spin) {
    font-size: 14px;
    width: 100% !important;
    height: calc(100% - 24px) !important;

    :global(.semi-spin-wrapper) {
      color: #ccc !important;
    }
  }

  :global(.semi-spin-children) {
    width: 100%;
    height: 100%;
  }
}

.meetingSummaryHeader {
  width: 60%;
  height: 56px;
  min-height: 56px;
  background: #fff;
  display: flex;
  align-items: center;
  margin-left: 25px;
}

.meetingSummaryGlobalSearch {
  position: absolute;
  top: 10px;
  right: 10px;
  width: auto !important;
}

.meetingSummaryContainer {
  position: relative;
  max-width: 800px;
  height: 100%;
  margin: 0 auto;
  background: #fff;
}

.transcriptContainer {
  flex: 1 1 auto;
  overflow-y: scroll;
  padding: 0 40px 150px;
  height: 100vh;
  // 隐藏滚动条
  scrollbar-width: none;
  scroll-behavior: smooth;
  max-width: 800px;
  margin: 0 auto;
  outline: none;
}

.transcriptItem {
  border-radius: 8px;
  margin-bottom: 16px;
}

.transcriptItem:hover .editButton {
  display: inline-block !important;
}

.transcriptItemActive {
  .transcriptText {
    line-height: 24px;
    border: 1px solid #005bf8;
    box-shadow: 0 0 0 2px #e5eefe;
  }
}

.playButton {
  background-color: #fff !important;
}

.transcriptMeta {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.transcriptTime {
  font-variant-numeric: tabular-nums;
}

.transcriptAvatar {
  width: 24px;
}

.transcriptSpeaker {
  font-size: 12px;
  color: #000;
  line-height: 20px;
}

.transcriptSummaryText {
  font-size: 14px;
  color: #000;
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  line-height: 24px;
}

.transcriptText {
  font-size: 14px;
  line-height: 24px;
  color: #000;
  padding: 16px;
  border-radius: 8px;
  background: #f6f8fd;
  cursor: pointer;
}

.editButton {
  font-size: 12px;
  display: none !important;
  height: 24px !important;
  padding: 2px 6px !important;
  margin-left: -8px;

  &:hover {
    background: #fff !important;
  }
}

.saveAction {
  margin-left: 4px;
  margin-top: -2px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.editTextarea {
  font-size: 14px;
  border: 1px solid #005bf8;
  border-radius: 4px;
  background-color: #fff !important;
}

.audioBar {
  position: sticky;
  bottom: 0;
  width: 100%;
  background: #fff;
  padding: 32px 0;
  z-index: 10;
}

.seekBar {
  position: relative;
  width: 100%;
  height: 6px;
  background: #ebeef2;
  border-radius: 999px;
  cursor: pointer;
}

.seekBarProgress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #1677ff;
  border-radius: 999px;
}

.audioControls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
}

.controlGroup {
  display: flex;
  align-items: center;
  gap: 4px;

  :global(.semi-button) {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

.rateSelect {
  margin-top: -2px;
  margin-right: 5px;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.audioButton {
  margin-left: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.audioButton:hover {
  border-color: #1677ff;
  color: #1677ff;
}

.timeInfo {
  font-variant-numeric: tabular-nums;
  font-size: 12px;
  color: #555;
  white-space: nowrap;
  margin-left: 8px;
}

.playbackRateSelect {
  cursor: pointer;

  :global(.semi-tag-white-light) {
    width: 25px;
    font-size: 14px;
    text-align: center;
    color: #005bf8;
    border: none;
    padding: 4px 0;
  }

  :global(.semi-tag-content) {
    flex: none;
  }
}

.scrollControls {
  position: fixed;
  right: 40px;
  bottom: 104px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 20;
}

// 双屏后的控制按钮
.scrollControlsWrap {
  position: absolute !important;
  right: 10px !important;
}

.scrollButton {
  appearance: none;
  border: 1px solid #d9d9d9;
  padding: 8px 10px;
  border-radius: 20px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 8%);
}

.scrollButton:hover {
  border-color: #1677ff;
  color: #1677ff;
}

// 新增样式类
.volumeSlider {
  width: 80px;
  margin-left: 6px;
}

.progressControl {
  margin-top: 8px;
  width: 100%;
}

.progressSlider {
  width: 573px;
  height: 6px;
  appearance: none;
  background: #ebeef2;
  border-radius: 10px;
  outline: none;
}

.progressSlider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #1677ff;
  border-radius: 50%;
  cursor: pointer;
}

.progressSlider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #1677ff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.scrollIcon {
  cursor: pointer;
  border-radius: 8px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 10%);
  transition: all 0.1s ease-in-out;

  &:hover {
    transform: scale(1.15);
  }
}

.scrollIconBottom {
  transform: rotate(180deg);

  &:hover {
    transform: rotate(180deg) scale(1.15);
  }
}

.meetingSummaryWrapper {
  // position: relative;
}

.meetingSummaryRightPane {
  padding: 24px 40px;
  overflow-y: scroll;

  .title {
    color: #000;
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
  }

  .renderTitle {
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    color: #000;
    margin-bottom: 16px;
    margin-top: 24px;
  }

  .scrollControls {
    bottom: 40px;
    right: 15px;
  }
}

.renderContentPre {
  white-space: break-spaces;
}

.keywordsRender {
  .renderContent {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .keywordsRenderTag {
    height: 32px;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    color: #fff;
    background: #005bf8;
  }
}

.spinLoading {
  position: absolute;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

