import Otherpolicy from '@/assets/office/otherpolicy.svg';
import Gwyzc from '@/assets/office/policy.svg';
import Example from '@/components/Example';
import { getPolExpMenuList, PolExpMenuListType } from '@/services/policyInterpretation';
import { calculateAndSetHeight, convertTextToRichInput, dispatchInUtils } from '@/utils';
import { Card, Typography } from '@douyinfe/semi-ui';
import { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'umi';
import styles from './index.less';

type Props = {
  getSelectActiveTab: (e: number) => void;
  getSelectAvtivePrompt: (e: string) => void;
  sendMessage: (e: string) => void;
};

// 政策解读默认富文本内容
const defaultIcon = {
  5001: {
    Icon: Gwyzc,
  },
  5002: {
    Icon: Otherpolicy,
  },
};

const PolicyInterpretation: React.FC<Props> = ({
  getSelectActiveTab,
  getSelectAvtivePrompt,
  sendMessage,
}) => {
  const { Text, Paragraph } = Typography;
  const dispatch = useDispatch();
  const policyInterpretationRef = useRef<HTMLDivElement | null>(null);
  const [policyInterpretationList, setPolicyInterpretationList] = useState<PolExpMenuListType[]>(
    [],
  );
  const [policyInterpretationItem, setPolicyInterpretationItem] = useState<PolExpMenuListType>();
  const [activeTab, setActiveTab] = useState(0);
  const getPolicyInterpretationList = async () => {
    try {
      const res = await getPolExpMenuList();
      if (res.data) {
        setActiveTab(res.data[0].routeId);
        setPolicyInterpretationItem(res.data[0]);
        setPolicyInterpretationList(
          res.data.map((i) => {
            return {
              ...i,
              icon: defaultIcon[i.routeId as keyof typeof defaultIcon]?.Icon || '',
            };
          }),
        );
      }
    } catch (error) {
      console.error('Failed to get active tab:', error);
    }
  };

  const setActiveTabFun = (routeId: number) => {
    if (routeId === activeTab) return;
    const selectActive = policyInterpretationList.filter((item) => item.routeId === routeId);
    setActiveTab(routeId);
    setPolicyInterpretationItem(selectActive[0]);
    dispatchInUtils({
      type: 'chat/updateRichTextAreaContent',
      payload: '',
    });
  };

  useEffect(() => {
    getPolicyInterpretationList();
    const element = document.querySelector('.semi-chat-inner');
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach(() => {
          calculateAndSetHeight(policyInterpretationRef);
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    return () => {};
  }, []);

  useEffect(() => {
    const handleResize = () => {
      calculateAndSetHeight(policyInterpretationRef);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (activeTab) {
      dispatch({
        type: 'chat/setRichTextContent',
        payload: {
          useRichText: true,
          richTextValue: convertTextToRichInput(policyInterpretationItem?.config.prompt || ''),
        },
      });
      getSelectActiveTab(activeTab);
      getSelectAvtivePrompt(policyInterpretationItem?.config.prompt || '');
    }
  }, [activeTab, policyInterpretationItem]);

  return (
    <div ref={policyInterpretationRef} className={styles.policyInterpretation}>
      <div className={styles.policyTab}>
        {policyInterpretationList.map((item: PolExpMenuListType) => (
          <div onClick={() => setActiveTabFun(item.routeId)} key={item.routeId}>
            <Card
              shadows="hover"
              className={styles.policyTabIn}
              style={{ borderColor: activeTab === item.routeId ? '#005BF8' : '#ebeef2' }}
            >
              <img src={item.icon} alt="" className={styles.gwyzc} />
              <div className={styles.gwyzcText}>{item.desc}</div>
              <Text
                ellipsis={{
                  rows: 2,
                  showTooltip: { opts: { style: { width: 162 } } },
                }}
                className={styles.gwyzcDesc}
              >
                {item.config.textDesc}
              </Text>
            </Card>
          </div>
        ))}
      </div>
      <Example
        description="你可以发起对政策的提问，智能助手帮你解读！"
        examplesTitle="提示示例"
        examplesDescription="你可以点击下列示例，智能助手帮你解读"
        questions={policyInterpretationItem?.config?.questions || []}
        isStoreWebSite={true}
        sendMessage={sendMessage}
      />
    </div>
  );
};

export default PolicyInterpretation;
